# 简单防火墙配置工具

## 🚀 使用方法

### 1. 修改配置文件
```bash
nano .env
```

### 2. 执行防火墙配置
```bash
chmod +x firewall.sh
./firewall.sh
```

## 📝 配置说明

### .env 文件格式
```bash
# SSH允许访问的IP地址 (逗号分隔)
ALLOWED_IPS="*******,*******,**********"

# 公开访问的端口 (逗号分隔)
PUBLIC_PORTS="80,443,8080,3000"
```

## 📋 常用配置示例

### Kafka集群
```bash
ALLOWED_IPS="你的IP1,你的IP2"
PUBLIC_PORTS="8081,28080,29092,2181,9092"
```

### Web服务器
```bash
ALLOWED_IPS="你的IP1,你的IP2"
PUBLIC_PORTS="80,443,3000"
```

### 数据库服务器
```bash
ALLOWED_IPS="你的IP1,你的IP2"
PUBLIC_PORTS="3306,5432,6379"
```

## 🔧 管理命令

```bash
# 查看防火墙规则
iptables -L -n

# 重启防火墙服务
systemctl restart iptables-restore.service

# 清除所有规则 (紧急恢复)
iptables -F && iptables -P INPUT ACCEPT
```

## ⚠️ 注意事项

1. 确保你的IP在 `ALLOWED_IPS` 中，否则会断开SSH连接
2. 脚本会自动备份当前规则到 `/root/iptables_backup_时间戳.txt`
3. 配置会在重启后自动恢复
