```bash
# 2025年7月18日
1. claw几台vps遭受到了ddos攻击1次，被停机了。
2. 为了安全起见，设置自定义ip地址才可以入站范围vps
3. 配置脚本 setup_firewall.sh
4. 执行方法：建议：先安装基础包，然后配置 iptables，最后再部署 sing-box 服务。这样可以确保所有组件都能正常工作。
	- 执行 debian常用库一键安装 ，注意iptables不要与ufw等库冲突
	- 选择 iptables 与 sing-box 的兼容性更好,更精确的控制
	- 保存或者上传脚本到vps机器 setup_firewall.sh
	- 赋予执行权限 chmod +x /root/setup_firewall.sh
	- 执行脚本 /root/setup_firewall.sh


```



```bash
# 取消限制，完全清除防火墙规则，执行下面的命令

# 清空所有规则
iptables -F
iptables -X
iptables -Z

# 设置默认策略为允许
iptables -P INPUT ACCEPT
iptables -P FORWARD ACCEPT
iptables -P OUTPUT ACCEPT

# 停止并禁用自动加载服务
systemctl stop iptables-restore.service
systemctl disable iptables-restore.service

# 删除保存的规则文件
rm -f /etc/iptables/rules.v4


```

