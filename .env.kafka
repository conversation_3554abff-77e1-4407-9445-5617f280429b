# Kafka + Flink 集群专用配置
# 适用于运行 Docker Compose Kafka 集群的VPS

# ===== SSH访问控制 =====
ALLOWED_IPS="************,***************,***************"

# ===== Kafka + Flink 服务端口 =====
PUBLIC_PORTS="8081:Flink JobManager UI,28080:Redpanda Console,29092:Kafka External,2181:Zookeeper,9092:Kafka Internal"

# ===== 可选配置 =====
ENABLE_LOGGING="true"
LOG_LIMIT="10"
BACKUP_DIR="/root/iptables_backups"

# ===== VPS配置 =====
VPS_EXTERNAL_IP="*************"
SERVER_NAME="Kafka-Flink-Cluster"

# ===== 高级配置 =====
ALLOW_PING="true"
ALLOW_ALL_OUTBOUND="true"
CONNECTION_TIMEOUT="600"
