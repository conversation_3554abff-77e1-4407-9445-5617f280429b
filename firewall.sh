#!/bin/bash
# 简单防火墙配置脚本 - 读取.env文件配置

# 检查root权限
if [[ $EUID -ne 0 ]]; then
   echo "❌ 请使用root用户运行"
   exit 1
fi

# 检查.env文件
if [[ ! -f ".env" ]]; then
    echo "❌ 找不到.env文件"
    exit 1
fi

# 加载.env配置
source .env

echo "🔥 开始配置防火墙..."

# 备份当前规则
iptables-save > "/root/iptables_backup_$(date +%Y%m%d_%H%M%S).txt"

# 清空规则
iptables -F
iptables -X
iptables -Z

# 设置默认策略
iptables -P INPUT DROP
iptables -P FORWARD DROP
iptables -P OUTPUT ACCEPT

# 允许本地回环
iptables -A INPUT -i lo -j ACCEPT

# 允许已建立的连接
iptables -A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT

# 添加允许的IP (SSH访问)
echo "🛡️ 添加允许的IP..."
IFS=',' read -ra IPS <<< "$ALLOWED_IPS"
for ip in "${IPS[@]}"; do
    ip=$(echo "$ip" | xargs)  # 去除空格
    if [[ -n "$ip" ]]; then
        iptables -A INPUT -s "$ip" -j ACCEPT
        echo "✅ 允许IP: $ip"
    fi
done

# 开放公共端口
echo "🌐 开放公共端口..."
IFS=',' read -ra PORTS <<< "$PUBLIC_PORTS"
for port in "${PORTS[@]}"; do
    port=$(echo "$port" | xargs)  # 去除空格
    if [[ -n "$port" && "$port" =~ ^[0-9]+$ ]]; then
        iptables -A INPUT -p tcp --dport "$port" -j ACCEPT
        echo "✅ 开放端口: $port"
    fi
done

# 保存规则
mkdir -p /etc/iptables
iptables-save > /etc/iptables/rules.v4

# 创建开机自启服务
cat > /etc/systemd/system/iptables-restore.service << 'EOF'
[Unit]
Description=Restore iptables rules
After=network.target

[Service]
Type=oneshot
ExecStart=/sbin/iptables-restore /etc/iptables/rules.v4
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
systemctl enable iptables-restore.service
systemctl start iptables-restore.service

echo ""
echo "✅ 防火墙配置完成！"
echo "🛡️ SSH允许IP: $ALLOWED_IPS"
echo "🌐 开放端口: $PUBLIC_PORTS"
echo ""
echo "查看规则: iptables -L -n"
