# Web服务器专用配置
# 适用于运行Web应用的VPS

# ===== SSH访问控制 =====
ALLOWED_IPS="YOUR_ADMIN_IP_1,YOUR_ADMIN_IP_2"

# ===== Web服务端口 =====
PUBLIC_PORTS="80:HTTP,443:HTTPS,3000:Node.js App,8080:Alternative Web,9000:Admin Panel"

# ===== 可选配置 =====
ENABLE_LOGGING="true"
LOG_LIMIT="20"
BACKUP_DIR="/root/iptables_backups"

# ===== VPS配置 =====
VPS_EXTERNAL_IP="YOUR_VPS_IP"
SERVER_NAME="Web-Server"

# ===== 高级配置 =====
ALLOW_PING="true"
ALLOW_ALL_OUTBOUND="true"
CONNECTION_TIMEOUT="600"
