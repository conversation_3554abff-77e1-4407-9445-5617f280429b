#!/bin/bash
# VPS 防火墙一键配置脚本
# 只允许指定IP访问，出站无限制，防止DDoS攻击

echo "=== VPS 防火墙配置开始 ==="

# 检查是否为root用户
if [[ $EUID -ne 0 ]]; then
   echo "❌ 错误：请使用root用户运行此脚本"
   exit 1
fi

# 定义允许的IP地址
ALLOWED_IPS=(
    "************"
    "***************"
    "***************"
)

# 获取当前SSH连接的IP
CURRENT_SSH_IP=$(echo ${SSH_CLIENT} | awk '{print $1}')
echo "当前SSH连接IP: ${CURRENT_SSH_IP}"

# 检查当前SSH IP是否在允许列表中
IP_IN_LIST=false
for ip in "${ALLOWED_IPS[@]}"; do
    if [[ "$CURRENT_SSH_IP" == "$ip" ]]; then
        IP_IN_LIST=true
        break
    fi
done

if [[ "$IP_IN_LIST" == false ]]; then
    echo "⚠️  警告：您的SSH连接IP (${CURRENT_SSH_IP}) 不在允许列表中！"
    echo "⚠️  配置完成后您将无法连接到此服务器！"
    echo "⚠️  请确认您的IP是否正确，或修改脚本中的ALLOWED_IPS数组"
    echo ""
    echo "按 Ctrl+C 取消，或按 Enter 继续（风险自负）"
    read -r
fi

echo ""
echo "=== 开始配置 iptables 防火墙 ==="

# 1. 备份当前iptables规则
echo "📁 备份当前 iptables 规则..."
BACKUP_FILE="/root/iptables_backup_$(date +%Y%m%d_%H%M%S).txt"
iptables-save > "$BACKUP_FILE"
echo "✅ 备份完成: $BACKUP_FILE"

# 2. 清空现有规则
echo "🧹 清空现有 iptables 规则..."
iptables -F
iptables -X
iptables -Z
echo "✅ 清空完成"

# 3. 设置默认策略
echo "🔧 设置默认策略..."
iptables -P INPUT DROP      # 默认拒绝所有入站
iptables -P FORWARD DROP    # 默认拒绝转发
iptables -P OUTPUT ACCEPT   # 允许所有出站（无限制）
echo "✅ 默认策略设置完成"

# 4. 允许本地回环接口
echo "🔄 配置本地回环接口..."
iptables -A INPUT -i lo -j ACCEPT
iptables -A OUTPUT -o lo -j ACCEPT
echo "✅ 本地回环接口配置完成"

# 5. 允许已建立的连接
echo "🔗 允许已建立的连接..."
iptables -A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT
echo "✅ 已建立连接规则添加完成"

# 6. 添加允许的IP地址
echo "🛡️  添加允许的IP地址..."
for ip in "${ALLOWED_IPS[@]}"; do
    iptables -A INPUT -s "$ip" -j ACCEPT
    echo "✅ 已添加允许IP: $ip"
done

# 6.5. 开放Docker服务端口给所有人访问
echo "🐳 开放Docker服务端口..."
# Flink JobManager Web UI
iptables -A INPUT -p tcp --dport 8081 -j ACCEPT
echo "✅ 已开放端口 8081 (Flink JobManager UI)"

# Redpanda Console (Kafka管理界面)
iptables -A INPUT -p tcp --dport 28080 -j ACCEPT
echo "✅ 已开放端口 28080 (Redpanda Console)"

# Kafka外部访问端口
iptables -A INPUT -p tcp --dport 29092 -j ACCEPT
echo "✅ 已开放端口 29092 (Kafka External)"

# Zookeeper端口 (可选，如果需要外部访问)
iptables -A INPUT -p tcp --dport 2181 -j ACCEPT
echo "✅ 已开放端口 2181 (Zookeeper)"

# Kafka内部端口 (可选，如果需要外部直接访问)
iptables -A INPUT -p tcp --dport 9092 -j ACCEPT
echo "✅ 已开放端口 9092 (Kafka Internal)"

echo "🌐 Docker服务端口开放完成"

# 7. 记录被拒绝的连接（可选，用于调试）
echo "📝 配置日志记录..."
iptables -A INPUT -m limit --limit 5/min -j LOG --log-prefix "IPTABLES-DENIED: " --log-level 7
echo "✅ 日志记录配置完成"

# 8. 保存iptables规则
echo "💾 保存 iptables 规则..."
mkdir -p /etc/iptables
iptables-save > /etc/iptables/rules.v4
echo "✅ 规则保存完成"

# 9. 创建自动恢复服务
echo "🔄 创建开机自动加载服务..."
cat > /etc/systemd/system/iptables-restore.service << 'EOF'
[Unit]
Description=Restore iptables rules
After=network.target

[Service]
Type=oneshot
ExecStart=/sbin/iptables-restore /etc/iptables/rules.v4
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
EOF

# 10. 启用自动加载服务
systemctl daemon-reload
systemctl enable iptables-restore.service
systemctl start iptables-restore.service
echo "✅ 自动加载服务配置完成"

echo ""
echo "=== 配置完成，显示当前规则 ==="
iptables -L -n -v --line-numbers

echo ""
echo "=== 测试连接 ==="
echo "🌐 测试出站连接..."
if ping -c 3 ******* >/dev/null 2>&1; then
    echo "✅ 出站连接正常"
else
    echo "❌ 出站连接异常"
fi

echo ""
echo "=== 配置摘要 ==="
echo "🛡️  允许SSH访问的IP地址:"
for ip in "${ALLOWED_IPS[@]}"; do
    echo "   - $ip"
done
echo "🐳 开放的Docker服务端口 (所有IP可访问):"
echo "   - 8081  (Flink JobManager UI)"
echo "   - 28080 (Redpanda Console - Kafka管理)"
echo "   - 29092 (Kafka外部访问)"
echo "   - 2181  (Zookeeper)"
echo "   - 9092  (Kafka内部端口)"
echo "🌐 出站连接: 无限制"
echo "🔒 SSH连接: 仅允许指定IP"
echo "🌍 Docker服务: 全球可访问"
echo "📁 备份文件: $BACKUP_FILE"
echo "📝 规则文件: /etc/iptables/rules.v4"

echo ""
echo "=== 重要提醒 ==="
echo "✅ 防火墙配置完成！"
echo "✅ 您的VPS现在只接受指定IP的访问"
echo "✅ 出站流量无任何限制"
echo "✅ 有效防止DDoS攻击"
echo ""
echo "🚨 如果需要恢复，请运行:"
echo "   systemctl stop iptables-restore.service"
echo "   iptables-restore < $BACKUP_FILE"
echo ""
echo "📋 常用管理命令:"
echo "   查看规则: iptables -L -n -v"
echo "   查看日志: tail -f /var/log/kern.log | grep IPTABLES-DENIED"
echo "   重启服务: systemctl restart iptables-restore.service"

echo ""
echo "🎉 配置完成！您的VPS现在已经安全加固！"
