# VPS 防火墙安全配置工具

## 🛡️ 脚本作用

### 安全防护功能
- **阻止陌生IP SSH登录** - 只允许指定IP地址SSH访问服务器
- **防止DDoS攻击** - 默认拒绝所有未授权的入站连接
- **限制端口访问** - 只开放必要的服务端口
- **保护服务器安全** - 大幅减少被攻击的风险

### 工作原理
1. **SSH访问控制**: 只有 `.env` 中配置的IP才能SSH登录
2. **端口白名单**: 只开放 `.env` 中指定的端口给全球访问
3. **出站无限制**: 服务器可以正常访问外网
4. **连接跟踪**: 允许已建立连接的返回数据

## 📋 配置文件说明

### .env 文件格式
```bash
# SSH允许访问的IP地址 (逗号分隔)
ALLOWED_IPS="*******,*******"

# 公开访问的端口 (逗号分隔)  
PUBLIC_PORTS="80,443,8080"
```

### 常用配置示例

**Web服务器**:
```bash
ALLOWED_IPS="你的家庭IP,你的办公室IP"
PUBLIC_PORTS="80,443"
```

**Kafka集群**:
```bash
ALLOWED_IPS="管理员IP1,管理员IP2"
PUBLIC_PORTS="8081,28080,29092,2181,9092"
```

**数据库服务器**:
```bash
ALLOWED_IPS="开发者IP1,开发者IP2"
PUBLIC_PORTS="3306,5432"
```

## 🚀 部署操作指导

### 0. 系统环境准备 (重要!)

#### 方法1: 使用自动化脚本 (推荐)
```bash
# 上传并执行系统准备脚本
chmod +x prepare_system.sh
./prepare_system.sh
```

#### 方法2: 手动安装配置
```bash
# Debian/Ubuntu 系统
apt update
apt install -y iptables iptables-persistent netfilter-persistent

# CentOS/RHEL 系统
yum install -y iptables-services

# 禁用冲突的防火墙
ufw disable                    # 禁用ufw
systemctl stop firewalld      # 停止firewalld
systemctl disable firewalld   # 禁用firewalld

# 验证iptables可用
iptables --version
iptables -L -n
```

#### 重要说明
- **iptables与ufw/firewalld冲突**: 必须禁用其他防火墙工具
- **sing-box兼容性**: iptables提供更精确的控制，与sing-box兼容性更好
- **建议顺序**: 先安装基础包 → 配置iptables → 部署sing-box服务

### 1. 上传文件到VPS
```bash
# 方法1: 使用scp上传
scp firewall.sh .env root@你的VPS_IP:/root/

# 方法2: 直接在VPS上创建文件
nano firewall.sh  # 复制脚本内容
nano .env         # 配置IP和端口
```

### 2. 修改配置文件
```bash
# 编辑配置文件
nano .env

# 重要: 确保你当前的IP在ALLOWED_IPS中！
# 否则执行后会断开SSH连接
```

### 3. 赋予执行权限
```bash
chmod +x firewall.sh
```

### 4. 执行防火墙配置
```bash
# 以root用户执行
./firewall.sh

# 如果不是root用户，使用sudo
sudo ./firewall.sh
```

### 5. 部署其他服务 (可选)
```bash
# 防火墙配置完成后，可以安全部署其他服务
# 例如: sing-box, docker服务等

# Docker服务示例
docker-compose up -d

# sing-box服务示例
systemctl start sing-box
systemctl enable sing-box
```

### 6. 检查配置效果

#### 查看防火墙规则
```bash
# 查看当前iptables规则
iptables -L -n -v

# 查看具体端口规则
iptables -L INPUT -n --line-numbers
```

#### 测试SSH访问
```bash
# 从允许的IP测试SSH (应该成功)
ssh root@VPS_IP

# 从其他IP测试SSH (应该被拒绝)
```

#### 测试服务端口
```bash
# 测试开放的端口 (应该可以访问)
telnet VPS_IP 80
curl http://VPS_IP:8080

# 测试未开放的端口 (应该被拒绝)
telnet VPS_IP 3306
```

#### 查看连接状态
```bash
# 查看当前连接
netstat -tulnp

# 查看防火墙日志
tail -f /var/log/kern.log | grep iptables
```

## 🔧 管理命令

### 查看状态
```bash
# 查看防火墙规则
iptables -L -n

# 查看服务状态
systemctl status iptables-restore.service

# 查看备份文件
ls -la /root/iptables_backup_*.txt
```

### 修改配置
```bash
# 修改配置文件
nano .env

# 重新应用配置
./firewall.sh
```

### 紧急恢复
```bash
# 如果配置错误导致无法连接，可以通过VPS控制台执行:

# 方法1: 清除所有规则
iptables -F
iptables -P INPUT ACCEPT
iptables -P OUTPUT ACCEPT
iptables -P FORWARD ACCEPT

# 方法2: 恢复备份
iptables-restore < /root/iptables_backup_最新时间戳.txt

# 方法3: 停止防火墙服务
systemctl stop iptables-restore.service
```

## ⚠️ 重要注意事项

### 执行前必须检查
1. **确认当前IP**: 确保你的IP在 `ALLOWED_IPS` 中
2. **备份重要**: 脚本会自动备份，但建议手动记录配置
3. **测试环境**: 建议先在测试服务器上验证

### 安全提醒
- 执行后立即测试SSH连接
- 保留VPS控制台访问权限以备紧急恢复
- 定期更新允许的IP地址
- 只开放必要的服务端口

### 常见问题
**Q: 执行后无法SSH连接？**
A: 通过VPS控制台执行 `iptables -F && iptables -P INPUT ACCEPT`

**Q: 服务无法访问？**  
A: 检查端口是否在 `PUBLIC_PORTS` 中，检查服务是否正常运行

**Q: 如何添加新的IP？**
A: 修改 `.env` 文件中的 `ALLOWED_IPS`，重新执行 `./firewall.sh`

## 📊 配置效果

执行成功后，您的VPS将具备：
- ✅ SSH访问安全 (只允许指定IP)
- ✅ 服务端口开放 (按需配置)
- ✅ DDoS防护 (拒绝未授权连接)
- ✅ 出站正常 (不影响服务器访问外网)
- ✅ 开机自启 (重启后自动恢复配置)
