# VPS 防火墙配置工具

一个灵活、可配置的VPS防火墙配置脚本，支持通过 `.env` 文件进行环境特定的配置。

## 🚀 快速开始

### 1. 准备配置文件

```bash
# 方法1：使用现有的Kafka配置
cp .env.kafka .env

# 方法2：从示例创建自定义配置
cp .env.example .env
nano .env  # 修改配置
```

### 2. 执行防火墙配置

```bash
# 赋予执行权限
chmod +x setup_firewall_configurable.sh

# 执行配置
./setup_firewall_configurable.sh
```

## 📋 配置文件说明

### 主要配置项

| 配置项 | 说明 | 示例 |
|--------|------|------|
| `ALLOWED_IPS` | SSH访问允许的IP地址 | `"*******,*******"` |
| `PUBLIC_PORTS` | 公开访问的端口 | `"80:HTTP,443:HTTPS"` |
| `VPS_EXTERNAL_IP` | VPS外网IP地址 | `"*************"` |
| `ENABLE_LOGGING` | 是否启用日志记录 | `"true"` |

### 端口配置格式

```bash
# 格式：端口号:描述,端口号:描述
PUBLIC_PORTS="8081:Flink UI,28080:Kafka Console,29092:Kafka External"
```

## 📁 预设配置文件

| 文件 | 用途 | 说明 |
|------|------|------|
| `.env.kafka` | Kafka集群 | 包含Flink、Kafka、Zookeeper端口 |
| `.env.web` | Web服务器 | 包含HTTP、HTTPS等Web端口 |
| `.env.example` | 通用模板 | 可复制修改的配置模板 |

## 🔧 使用场景

### Kafka + Flink 集群

```bash
cp .env.kafka .env
# 修改 ALLOWED_IPS 为您的IP
./setup_firewall_configurable.sh
```

### Web 服务器

```bash
cp .env.web .env
# 修改配置后执行
./setup_firewall_configurable.sh
```

### 自定义配置

```bash
cp .env.example .env
# 根据需要修改所有配置项
./setup_firewall_configurable.sh
```

## 🛡️ 安全特性

- ✅ SSH访问限制到指定IP
- ✅ 公共服务端口可配置开放
- ✅ 自动备份现有规则
- ✅ 开机自动恢复规则
- ✅ 连接日志记录
- ✅ 出站连接无限制

## 📝 管理命令

```bash
# 查看当前规则
iptables -L -n -v

# 查看拒绝日志
tail -f /var/log/kern.log | grep IPTABLES-DENIED

# 重启防火墙服务
systemctl restart iptables-restore.service

# 修改配置
nano .env
./setup_firewall_configurable.sh  # 重新应用
```

## 🚨 恢复方法

如果配置出现问题，可以恢复到之前的状态：

```bash
# 停止自动恢复服务
systemctl stop iptables-restore.service

# 恢复备份（查看备份文件）
ls /root/iptables_backups/
iptables-restore < /root/iptables_backups/iptables_backup_YYYYMMDD_HHMMSS.txt

# 或者完全清除规则
iptables -F && iptables -X && iptables -Z
iptables -P INPUT ACCEPT && iptables -P FORWARD ACCEPT && iptables -P OUTPUT ACCEPT
```

## ⚠️ 重要提醒

1. **执行前确认IP**：确保您的当前IP在 `ALLOWED_IPS` 列表中
2. **测试连接**：配置后测试SSH和服务访问是否正常
3. **备份重要**：脚本会自动备份，但建议手动记录重要配置
4. **生产环境**：建议先在测试环境验证配置

## 🔄 版本对比

| 特性 | 原版脚本 | 可配置版本 |
|------|----------|------------|
| IP配置 | 硬编码 | ✅ .env文件 |
| 端口配置 | 硬编码 | ✅ .env文件 |
| 多环境支持 | ❌ | ✅ 预设配置 |
| 配置验证 | 基础 | ✅ 完整验证 |
| 错误处理 | 基础 | ✅ 严格模式 |
| 便携性 | 低 | ✅ 高度便携 |
