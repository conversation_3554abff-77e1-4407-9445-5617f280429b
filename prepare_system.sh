#!/bin/bash
# VPS系统环境准备脚本 - 安装必要库并处理冲突

echo "🔧 开始准备VPS系统环境..."

# 检查root权限
if [[ $EUID -ne 0 ]]; then
   echo "❌ 请使用root用户运行"
   exit 1
fi

# 检测系统类型
if [ -f /etc/debian_version ]; then
    OS="debian"
    echo "✅ 检测到 Debian/Ubuntu 系统"
elif [ -f /etc/redhat-release ]; then
    OS="centos"
    echo "✅ 检测到 CentOS/RHEL 系统"
else
    echo "❌ 不支持的系统类型"
    exit 1
fi

echo ""
echo "📦 安装必要的系统库..."

if [ "$OS" = "debian" ]; then
    # Debian/Ubuntu 系统
    apt update
    apt install -y iptables iptables-persistent netfilter-persistent curl wget net-tools
    echo "✅ Debian系统库安装完成"
elif [ "$OS" = "centos" ]; then
    # CentOS/RHEL 系统
    yum install -y iptables-services curl wget net-tools
    echo "✅ CentOS系统库安装完成"
fi

echo ""
echo "🚫 处理防火墙冲突..."

# 检查并禁用ufw
if command -v ufw >/dev/null 2>&1; then
    echo "🔍 发现ufw，正在禁用..."
    ufw --force disable
    systemctl disable ufw >/dev/null 2>&1
    echo "✅ ufw已禁用"
else
    echo "✅ 未发现ufw"
fi

# 检查并禁用firewalld
if systemctl is-active --quiet firewalld; then
    echo "🔍 发现firewalld正在运行，正在停止..."
    systemctl stop firewalld
    systemctl disable firewalld
    echo "✅ firewalld已停止并禁用"
else
    echo "✅ firewalld未运行"
fi

echo ""
echo "🔧 配置iptables服务..."

# 启用iptables服务
if [ "$OS" = "debian" ]; then
    systemctl enable netfilter-persistent
elif [ "$OS" = "centos" ]; then
    systemctl enable iptables
fi

echo ""
echo "✅ 验证iptables可用性..."

# 测试iptables
if iptables --version >/dev/null 2>&1; then
    echo "✅ iptables命令可用: $(iptables --version | head -1)"
else
    echo "❌ iptables命令不可用"
    exit 1
fi

# 测试iptables-save
if iptables-save >/dev/null 2>&1; then
    echo "✅ iptables-save命令可用"
else
    echo "❌ iptables-save命令不可用"
    exit 1
fi

echo ""
echo "📋 当前系统状态:"
echo "   - 系统类型: $OS"
echo "   - iptables版本: $(iptables --version | head -1)"
echo "   - 当前规则数: $(iptables -L | grep -c '^Chain')"

echo ""
echo "🎉 系统环境准备完成！"
echo "📝 接下来可以执行:"
echo "   1. 配置 .env 文件"
echo "   2. 运行 ./firewall.sh"
echo "   3. 部署其他服务 (如sing-box, docker等)"
