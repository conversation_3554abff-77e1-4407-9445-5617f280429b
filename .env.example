# VPS 防火墙配置文件示例
# 复制此文件为 .env 并修改相应配置

# ===== SSH访问控制 =====
# 允许SSH访问的IP地址，用逗号分隔
# 请替换为您的实际IP地址
ALLOWED_IPS="YOUR_IP_1,YOUR_IP_2,YOUR_IP_3"

# ===== 公开服务端口 =====
# 需要对全球开放的端口，用逗号分隔
# 格式：端口号:描述
# 常见配置示例：
# - Web服务: "80:HTTP,443:HTTPS"
# - Kafka集群: "8081:Flink UI,28080:Kafka Console,29092:Kafka External"
# - 数据库: "3306:MySQL,5432:PostgreSQL,6379:Redis"
# - 监控: "3000:Grafana,9090:Prometheus"
PUBLIC_PORTS="80:HTTP,443:HTTPS"

# ===== 可选配置 =====
# 是否启用日志记录 (true/false)
ENABLE_LOGGING="true"

# 日志记录频率限制 (每分钟最多记录次数)
LOG_LIMIT="5"

# 备份目录
BACKUP_DIR="/root/iptables_backups"

# ===== VPS特定配置 =====
# VPS外网IP (用于服务配置)
VPS_EXTERNAL_IP="YOUR_VPS_IP"

# 服务器描述 (用于日志标识)
SERVER_NAME="My-VPS-Server"

# ===== 高级配置 =====
# 是否开放ICMP ping (true/false)
ALLOW_PING="true"

# 是否开放所有出站连接 (true/false)
ALLOW_ALL_OUTBOUND="true"

# 连接跟踪超时时间 (秒)
CONNECTION_TIMEOUT="600"
