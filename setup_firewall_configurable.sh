#!/bin/bash
# VPS 防火墙配置脚本 - 可配置版本
# 使用 .env 文件进行配置，支持不同VPS环境

set -euo pipefail  # 严格模式

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ENV_FILE="${SCRIPT_DIR}/.env"

echo "=== VPS 防火墙配置开始 (可配置版本) ==="

# 检查是否为root用户
if [[ $EUID -ne 0 ]]; then
   echo "❌ 错误：请使用root用户运行此脚本"
   exit 1
fi

# 检查 .env 文件是否存在
if [[ ! -f "$ENV_FILE" ]]; then
    echo "❌ 错误：找不到配置文件 $ENV_FILE"
    echo "请先创建 .env 文件并配置相关参数"
    exit 1
fi

# 加载配置文件
echo "📋 加载配置文件: $ENV_FILE"
source "$ENV_FILE"

# 验证必需的配置项
if [[ -z "${ALLOWED_IPS:-}" ]]; then
    echo "❌ 错误：ALLOWED_IPS 未配置"
    exit 1
fi

# 解析配置
echo "🔍 解析配置参数..."

# 将逗号分隔的IP转换为数组
IFS=',' read -ra ALLOWED_IPS_ARRAY <<< "$ALLOWED_IPS"
echo "✅ 允许的IP地址: ${#ALLOWED_IPS_ARRAY[@]} 个"

# 解析公开端口
PUBLIC_PORTS_ARRAY=()
if [[ -n "${PUBLIC_PORTS:-}" ]]; then
    IFS=',' read -ra PUBLIC_PORTS_ARRAY <<< "$PUBLIC_PORTS"
    echo "✅ 公开端口: ${#PUBLIC_PORTS_ARRAY[@]} 个"
fi

# 获取当前SSH连接的IP
CURRENT_SSH_IP=$(echo ${SSH_CLIENT} | awk '{print $1}')
echo "🔗 当前SSH连接IP: ${CURRENT_SSH_IP}"

# 检查当前SSH IP是否在允许列表中
IP_IN_LIST=false
for ip in "${ALLOWED_IPS_ARRAY[@]}"; do
    if [[ "$CURRENT_SSH_IP" == "$ip" ]]; then
        IP_IN_LIST=true
        break
    fi
done

if [[ "$IP_IN_LIST" == false ]]; then
    echo "⚠️  警告：您的SSH连接IP (${CURRENT_SSH_IP}) 不在允许列表中！"
    echo "⚠️  配置完成后您将无法连接到此服务器！"
    echo "⚠️  请确认您的IP是否正确，或修改 .env 文件中的ALLOWED_IPS"
    echo ""
    echo "按 Ctrl+C 取消，或按 Enter 继续（风险自负）"
    read -r
fi

echo ""
echo "=== 开始配置 iptables 防火墙 ==="

# 创建备份目录
BACKUP_DIR="${BACKUP_DIR:-/root/iptables_backups}"
mkdir -p "$BACKUP_DIR"

# 1. 备份当前iptables规则
echo "📁 备份当前 iptables 规则..."
BACKUP_FILE="${BACKUP_DIR}/iptables_backup_$(date +%Y%m%d_%H%M%S).txt"
iptables-save > "$BACKUP_FILE"
echo "✅ 备份完成: $BACKUP_FILE"

# 2. 清空现有规则
echo "🧹 清空现有 iptables 规则..."
iptables -F
iptables -X
iptables -Z
echo "✅ 清空完成"

# 3. 设置默认策略
echo "🔧 设置默认策略..."
iptables -P INPUT DROP      # 默认拒绝所有入站
iptables -P FORWARD DROP    # 默认拒绝转发
if [[ "${ALLOW_ALL_OUTBOUND:-true}" == "true" ]]; then
    iptables -P OUTPUT ACCEPT   # 允许所有出站
    echo "✅ 出站连接: 无限制"
else
    iptables -P OUTPUT DROP     # 限制出站
    echo "✅ 出站连接: 受限制"
fi
echo "✅ 默认策略设置完成"

# 4. 允许本地回环接口
echo "🔄 配置本地回环接口..."
iptables -A INPUT -i lo -j ACCEPT
iptables -A OUTPUT -o lo -j ACCEPT
echo "✅ 本地回环接口配置完成"

# 5. 允许已建立的连接
echo "🔗 允许已建立的连接..."
iptables -A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT
echo "✅ 已建立连接规则添加完成"

# 6. 添加允许的IP地址 (SSH访问)
echo "🛡️  添加SSH允许的IP地址..."
for ip in "${ALLOWED_IPS_ARRAY[@]}"; do
    # 去除空格
    ip=$(echo "$ip" | xargs)
    if [[ -n "$ip" ]]; then
        iptables -A INPUT -s "$ip" -j ACCEPT
        echo "✅ 已添加SSH允许IP: $ip"
    fi
done

# 7. 开放公共服务端口
if [[ ${#PUBLIC_PORTS_ARRAY[@]} -gt 0 ]]; then
    echo "🌐 开放公共服务端口..."
    for port_desc in "${PUBLIC_PORTS_ARRAY[@]}"; do
        # 解析端口和描述
        port=$(echo "$port_desc" | cut -d':' -f1 | xargs)
        desc=$(echo "$port_desc" | cut -d':' -f2- | xargs)
        
        if [[ -n "$port" && "$port" =~ ^[0-9]+$ ]]; then
            iptables -A INPUT -p tcp --dport "$port" -j ACCEPT
            echo "✅ 已开放端口 $port ($desc)"
        else
            echo "⚠️  跳过无效端口: $port_desc"
        fi
    done
    echo "🌐 公共服务端口开放完成"
fi

# 8. 可选：允许ICMP ping
if [[ "${ALLOW_PING:-true}" == "true" ]]; then
    echo "🏓 允许ICMP ping..."
    iptables -A INPUT -p icmp --icmp-type echo-request -j ACCEPT
    echo "✅ ICMP ping已允许"
fi

# 9. 可选：记录被拒绝的连接
if [[ "${ENABLE_LOGGING:-true}" == "true" ]]; then
    echo "📝 配置日志记录..."
    LOG_LIMIT="${LOG_LIMIT:-5}"
    iptables -A INPUT -m limit --limit "${LOG_LIMIT}/min" -j LOG --log-prefix "IPTABLES-DENIED: " --log-level 7
    echo "✅ 日志记录配置完成 (限制: ${LOG_LIMIT}/分钟)"
fi

# 10. 保存iptables规则
echo "💾 保存 iptables 规则..."
mkdir -p /etc/iptables
iptables-save > /etc/iptables/rules.v4
echo "✅ 规则保存完成"

# 11. 创建自动恢复服务
echo "🔄 创建开机自动加载服务..."
cat > /etc/systemd/system/iptables-restore.service << 'EOF'
[Unit]
Description=Restore iptables rules
After=network.target

[Service]
Type=oneshot
ExecStart=/sbin/iptables-restore /etc/iptables/rules.v4
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
EOF

# 12. 启用自动加载服务
systemctl daemon-reload
systemctl enable iptables-restore.service
systemctl start iptables-restore.service
echo "✅ 自动加载服务配置完成"

echo ""
echo "=== 配置完成，显示当前规则 ==="
iptables -L -n -v --line-numbers

echo ""
echo "=== 测试连接 ==="
echo "🌐 测试出站连接..."
if ping -c 3 ******* >/dev/null 2>&1; then
    echo "✅ 出站连接正常"
else
    echo "❌ 出站连接异常"
fi

echo ""
echo "=== 配置摘要 ==="
echo "🛡️  SSH允许访问的IP地址:"
for ip in "${ALLOWED_IPS_ARRAY[@]}"; do
    ip=$(echo "$ip" | xargs)
    [[ -n "$ip" ]] && echo "   - $ip"
done

if [[ ${#PUBLIC_PORTS_ARRAY[@]} -gt 0 ]]; then
    echo "🌐 开放的公共服务端口 (全球可访问):"
    for port_desc in "${PUBLIC_PORTS_ARRAY[@]}"; do
        port=$(echo "$port_desc" | cut -d':' -f1 | xargs)
        desc=$(echo "$port_desc" | cut -d':' -f2- | xargs)
        [[ -n "$port" ]] && echo "   - $port ($desc)"
    done
fi

echo "🌍 出站连接: ${ALLOW_ALL_OUTBOUND:-true}"
echo "🏓 ICMP Ping: ${ALLOW_PING:-true}"
echo "📝 日志记录: ${ENABLE_LOGGING:-true}"
echo "📁 备份文件: $BACKUP_FILE"
echo "📝 规则文件: /etc/iptables/rules.v4"
echo "⚙️  配置文件: $ENV_FILE"

echo ""
echo "=== 重要提醒 ==="
echo "✅ 防火墙配置完成！"
echo "✅ SSH访问限制已生效"
echo "✅ 公共服务端口已开放"
echo "✅ 配置已持久化"
echo ""
echo "🚨 如果需要恢复，请运行:"
echo "   systemctl stop iptables-restore.service"
echo "   iptables-restore < $BACKUP_FILE"
echo ""
echo "📋 常用管理命令:"
echo "   查看规则: iptables -L -n -v"
echo "   查看日志: tail -f /var/log/kern.log | grep IPTABLES-DENIED"
echo "   重启服务: systemctl restart iptables-restore.service"
echo "   修改配置: nano $ENV_FILE"
echo ""
echo "🎉 配置完成！您的VPS现在已经安全加固！"
